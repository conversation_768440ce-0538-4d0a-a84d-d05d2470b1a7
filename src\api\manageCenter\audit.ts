import { defHttp } from '/@/utils/http/axios';

/**
 * 审核相关API接口
 */
enum Api {
  // 获取资产处置审核详情（增值委托和自主委托都使用此接口）
  getAppreciationAuditDetail = '/hgy/entrustService/hgyAssetEntrust/queryEntrustById',
  // 获取增值委托竞价审核详情
  getAppreciationBiddingAuditDetail = '/hgy/auction/hgyAuctionItemTemp/queryOrderItemTempById',
  // 获取采购审核详情（增值委托和自主委托都使用此接口）
  getProcurementAuditDetail = '/hgy/entrustService/hgyProcurement/queryProcurementById',
  // 获取资产处置审核详情（与getAppreciationAuditDetail相同，保持兼容性）
  getSelfAuditDetail = '/hgy/entrustService/hgyAssetEntrust/queryEntrustById',
  // 获取自主委托竞价审核详情
  getSelfBiddingAuditDetail = '/hgy/auction/hgyAuction/queryOrderAuctionItemById',

  // 获取标的列表
  getAuctionItemsList = '/hgy/entrustService/hgyAuctionItem/list',

  // 新增的统一审核接口
  // 资产处置审核接口（不区分增值和自主）
  reviewAssetEntrust = '/hgy/entrustService/hgyAssetEntrust/review',
  // 采购审核接口（不区分增值和自主）
  reviewProcurement = '/hgy/entrustService/hgyProcurement/review',
  // 竞价审核接口
  reviewAuctionItemTemp = '/hgy/auction/hgyAuctionItemTemp/reviewOrderAndItemTemp',
}

/**
 * 审核详情接口
 */
export interface AuditDetail {
  id: string;
  entrustType: number;
  serviceType: number;
  projectName: string;
  relationUser: string;
  relationPhone: string;
  applicantUser: string;
  submitTime: string;
  status: number;
  // 其他详细字段根据实际需求添加
  [key: string]: any;
}

/**
 * 统一审核参数接口
 */
export interface ReviewParams {
  id: string; // 资产/采购/竞价的ID
  status: number; // 3-通过 4-拒绝
  auditOpinion?: string; // 审核意见
}

/**
 * 标的信息接口
 */
export interface AuctionItem {
  id: string;
  auctionName: string;
  itemTitle: string;
  itemType: number;
  startPrice: number;
  appraisalPrice: number;
  reservePrice?: number;
  deposit: number;
  quantity: number;
  unit: string;
  province: string;
  city: string;
  district: string;
  address: string;
  description?: string;
  auctionMode: number;
  showCommission?: string;
  freeBidTime?: string;
  timedBidTime?: string;
}

/**
 * 获取增值委托审核详情（资产处置）
 * @param id 委托单号
 * @returns Promise<AuditDetail>
 */
export const getAppreciationAuditDetail = (id: string) => {
  return defHttp.get<AuditDetail>({
    url: Api.getAppreciationAuditDetail,
    params: { id },
  });
};

/**
 * 获取增值委托竞价审核详情
 * @param id 委托单号
 * @returns Promise<any>
 */
export const getAppreciationBiddingAuditDetail = (id: string) => {
  return defHttp.get<any>({
    url: Api.getAppreciationBiddingAuditDetail,
    params: { id },
  });
};

/**
 * 获取自主委托审核详情（资产处置）
 * @param id 委托单号
 * @returns Promise<AuditDetail>
 */
export const getSelfAuditDetail = (id: string) => {
  return defHttp.get<AuditDetail>({
    url: Api.getSelfAuditDetail,
    params: { id },
  });
};

/**
 * 获取自主委托竞价审核详情
 * @param id 委托单号
 * @returns Promise<any>
 */
export const getSelfBiddingAuditDetail = (id: string) => {
  return defHttp.get<any>({
    url: Api.getSelfBiddingAuditDetail,
    params: { id },
  });
};

/**
 * 获取采购审核详情
 * @param id 采购ID
 * @returns Promise<any>
 */
export const getProcurementAuditDetail = (id: string) => {
  return defHttp.get<any>({
    url: Api.getProcurementAuditDetail,
    params: { id },
  });
};

/**
 * 资产处置审核接口（不区分增值和自主）
 * @param params 审核参数
 * @returns Promise<any>
 */
export const reviewAssetEntrust = (params: ReviewParams) => {
  return defHttp.post<any>({
    url: Api.reviewAssetEntrust,
    data: params,
  });
};

/**
 * 采购审核接口（不区分增值和自主）
 * @param params 审核参数
 * @returns Promise<any>
 */
export const reviewProcurement = (params: ReviewParams) => {
  return defHttp.post<any>({
    url: Api.reviewProcurement,
    data: params,
  });
};

/**
 * 竞价审核接口
 * @param params 审核参数
 * @returns Promise<any>
 */
export const reviewAuctionItemTemp = (params: ReviewParams) => {
  return defHttp.put<any>({
    url: Api.reviewAuctionItemTemp,
    data: params,
  });
};

/**
 * 根据服务类型统一审核接口
 * @param serviceType 服务类型 1-竞价委托 2-资产处置 3-采购信息
 * @param params 审核参数
 * @returns Promise<any>
 */
export const reviewByServiceType = (serviceType: number, params: ReviewParams) => {
  if (serviceType === 1) {
    // 竞价委托
    return reviewAuctionItemTemp(params);
  } else if (serviceType === 2) {
    // 资产处置
    return reviewAssetEntrust(params);
  } else if (serviceType === 3) {
    // 采购信息
    return reviewProcurement(params);
  } else {
    throw new Error(`不支持的服务类型: ${serviceType}`);
  }
};

/**
 * 根据委托类型和服务类型获取审核详情
 * @param id 委托单号
 * @param entrustType 委托类型 1-增值委托 2-自主委托 3-供求
 * @param serviceType 服务类型 1-竞价委托 2-资产处置 3-采购信息 4-供应 5-求购
 * @returns Promise<any>
 */
export const getAuditDetailByType = (id: string, entrustType: number, serviceType: number) => {
  // 根据服务类型调用不同的接口
  if (serviceType === 1) {
    // 竞价委托 - 根据委托类型区分
    if (entrustType === 1) {
      // 增值委托竞价
      return getAppreciationBiddingAuditDetail(id);
    } else {
      // 自主委托竞价
      return getSelfBiddingAuditDetail(id);
    }
  } else if (serviceType === 2) {
    // 资产处置 - 增值委托和自主委托都使用同一个接口
    return getAppreciationAuditDetail(id);
  } else if (serviceType === 3) {
    // 采购信息 - 增值委托和自主委托都使用同一个接口
    return getProcurementAuditDetail(id);
  } else if (serviceType === 4 || serviceType === 5) {
    // 供应需求 - 供应(4)和求购(5)都使用供应需求专用接口
    return defHttp.get({ url: '/hgy/supplyDemand/hgySupplyDemand/queryById', params: { id } });
  } else {
    // 默认返回资产处置接口
    return getAppreciationAuditDetail(id);
  }
};

/**
 * 获取标的列表
 * @param entrustOrderId 委托单号
 * @returns Promise<AuctionItem[]>
 */
export const getAuctionItemsList = (entrustOrderId: string) => {
  return defHttp.get<AuctionItem[]>({
    url: Api.getAuctionItemsList,
    params: { entrustOrderId },
  });
};
